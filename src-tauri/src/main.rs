
// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

fn main() {
    telex_lib::run()
}

async fn show_notification(title: String, body: String) -> Result<(), String> {
    use tauri::notification::Notification;
    Notification::new("com.mac.telex")
        .title(title)
        .body(body)
        .show()
        .map_err(|e| e.to_string())
}